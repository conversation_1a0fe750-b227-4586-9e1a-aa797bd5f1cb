/**
 * Electron 主进程 - 抖音自动化工具
 * 完全重构版本，用于重新打包
 * 版本: 2.5.0
 */

// ===== 依赖导入 =====
import { 
  app, 
  ipcMain, 
  session, 
  protocol, 
  BrowserWindow, 
  shell, 
  screen, 
  Menu, 
  dialog, 
  clipboard 
} from 'electron';
import UserAgent from 'user-agents';
import fetch from 'node-fetch';
import Proxy<PERSON>hain from 'proxy-chain';
import net from 'net';
import { createRequire } from 'node:module';
import { fileURLToPath } from 'node:url';
import path from 'node:path';
import os from 'node:os';
import fs from 'fs';
import 'fetch-blob';
import 'formdata-polyfill';
import machineId from 'node-machine-id';
import { keyboard, Key } from '@nut-tree/nut-js';
import Store from 'electron-store';
import crypto from 'node:crypto';

// ===== 全局常量 =====
const CURRENT_VERSION = "2.5.0";
const UPDATE_SERVER = "http://***************/pc/version.php";
const LICENSE_SERVER = "https://api.dushuren.tech/douyinUserFollower/licenseKey/checkV2";
const XOR_KEY = 972106363;

// ===== 全局变量 =====
const proxyServerMap = new Map();
const windowPauseStates = new Map();
const windowMetaList = [];
const taskQueue = [];
let isProcessingQueue = false;
let nextProxyPort = 18080;
let mainWindow = null;
let licenseCheckFailCount = 0;

// ===== 路径配置 =====
const __dirname = path.dirname(fileURLToPath(import.meta.url));
const APP_ROOT = path.join(__dirname, "../..");
const MAIN_DIST = path.join(APP_ROOT, "dist-electron");
const RENDERER_DIST = path.join(APP_ROOT, "dist");
const VITE_DEV_SERVER_URL = process.env.VITE_DEV_SERVER_URL;
const VITE_PUBLIC = VITE_DEV_SERVER_URL ? path.join(APP_ROOT, "public") : RENDERER_DIST;

const preloadPath = path.join(__dirname, "../preload/index.js");
const indexHtmlPath = path.join(RENDERER_DIST, "index.html");
const iconPath = app.isPackaged 
  ? path.join(RENDERER_DIST, "favicon.ico") 
  : path.join(__dirname, "../../favicon.ico");

// ===== 数据存储 =====
const store = new Store();
const { machineIdSync } = machineId;

// ===== 评论模板 =====
const commentTemplates = [
  "愿家人喜乐盈满人生，春暖花开心情好，快乐长伴如星辰。",
  "愿你和家人好运常伴左右，春暖花开心情好，平安护佑每刻。",
  "祝福家人喜乐盈满人生，每天皆有好心情，福气连连好运来。",
  "你这条视频我竟然看了三遍",
  "画面挺简单但意外有感觉",
  "感觉你平时是个特别细心的人",
  "这种节奏特别舒服，挺耐看",
  "不太会拍但挺有味道的",
  "你这条我忍不住点赞了",
  "看着挺稳的人，气质也好",
  "视频有点意思，很想看下一条",
  "说不上哪里好，反正挺吸引人",
  "你笑起来挺有感染力的",
  "镜头里透着一种特别的真实",
  "你这样发很自然，让人喜欢",
  "动作不多，但挺让人想看完",
  "不刻意的样子最打动人",
  "你这风格我还真挺喜欢",
  "普通场景但拍出了感觉",
  "视频挺随意的，我却很喜欢",
  "你讲话方式我还挺想听的",
  "看你做事特别安心的感觉",
  "气质这种东西，藏不住的",
  "每个动作都看得出生活气",
  "我居然被你的视频治愈了",
  "刷到你心情忽然变好了",
  "拍得挺简单但挺走心的",
  "这种静静拍的风格真不错",
  "视频好像没剪辑，但挺耐看",
  "像你这样的人挺少见了"
];

// ===== 缓存配置 =====
const cachePaths = [
  app.getPath("userData"), 
  app.getPath("appData"), 
  app.getPath("temp"), 
  app.getPath("documents"), 
  app.getPath("downloads"), 
  path.join(app.getPath("home"), "AppData", "Local")
];

const cacheConfigs = [
  { days: 2, file: ".sys_cache_a_" },
  { days: 4, file: ".cache_b_" },
  { days: 6, file: ".log_c_" },
  { days: 8, file: ".data_d_" },
  { days: 10, file: ".runtime_e_" },
  { days: 12, file: ".tmp_f_" }
];

// ===== 工具函数 =====

/**
 * HTTP POST 请求封装
 */
async function postRequest(url, data, headers = {}) {
  const response = await fetch(url, {
    method: "POST",
    headers: {
      'Content-Type': "application/json",
      ...headers
    },
    body: JSON.stringify(data)
  });
  
  if (!response.ok) {
    throw new Error(`HTTP error! status: ${response.status}`);
  }
  
  return await response.json();
}

/**
 * RSA 公钥解密
 */
function decryptWithPublicKey(encryptedData, publicKey) {
  const encrypted = Buffer.from(encryptedData, "base64");
  const decrypted = crypto.publicDecrypt({
    key: publicKey,
    padding: crypto.constants.RSA_PKCS1_PADDING
  }, encrypted);
  return decrypted.toString("utf8");
}

/**
 * 数据编码/解码（用于缓存文件）
 */
function encodeData(data) {
  return Buffer.from((data ^ XOR_KEY).toString()).toString("base64");
}

function decodeData(encodedData) {
  try {
    return parseInt(Buffer.from(encodedData, "base64").toString()) ^ XOR_KEY;
  } catch {
    return 0;
  }
}

/**
 * 生成随机数
 */
function randomInt(min, max) {
  return Math.floor(min + Math.random() * (max - min + 1));
}

/**
 * 延迟函数
 */
function delay(ms) {
  return new Promise(resolve => setTimeout(resolve, ms));
}

/**
 * 检查更新功能
 */
async function checkForUpdate() {
  try {
    const updateInfo = await postRequest(UPDATE_SERVER, {});
    console.log("Update check result:", updateInfo);
    
    const { version: latestVersion, downloadUrl } = updateInfo;
    console.info("最新版本：" + latestVersion);
    
    if (latestVersion !== CURRENT_VERSION) {
      const dialogOptions = {
        type: "info",
        title: "发现新版本",
        message: `当前版本：${CURRENT_VERSION}\n最新版本：${latestVersion}\n是否前往下载新版本？`,
        buttons: ["立即下载", "稍后更新"]
      };
      
      const { response: userChoice } = await dialog.showMessageBox(dialogOptions);
      if (userChoice === 0) {
        shell.openExternal(downloadUrl);
      }
    } else {
      console.info("当前已是最新版本");
    }
  } catch (error) {
    console.error("检查更新失败：", error.message);
  }
}

/**
 * 许可证验证
 */
async function validateLicense() {
  const licenseKey = store.get("kami_v1");
  if (!licenseKey) {
    console.warn("No license key found");
    return;
  }
  
  try {
    const response = await postRequest(LICENSE_SERVER, {
      deviceId: machineIdSync(true),
      licenseKey: licenseKey,
      platform: 'pc'
    });
    
    const publicKey = `-----BEGIN PUBLIC KEY-----
MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEA9ePb4Olr+QmDKhDFTBJf
j/mQeEq9QGReGKi63Sz8h+dbk4ZAwLyAYfpdl/vAFcbstdUaxR2vFzukNRkfrKG8
xuRNrZewxVp0Wl75VUL4XsoIsY2zYvadC2Iax/kFbDzGtWZBcLSq18b9OE+CS41Z
JN2gxcQHcbZTRGbl5zvGtQZXcSamKmreiRkd3EpWWlHdDUGQH/TSgCFXkazVAR96
WYFQHRxPaWFFpJBeGscKrya/AsC37zHpC4+3rSae6TKAYRs77GOqhJJG/orREWES
EMrcg1ss5M28SGz/h1MI20kLLaQUeKtDMGpUvqlUKUige4XD4iRgvtNlljvbtXZ/
DwIDAQAB
-----END PUBLIC KEY-----`;
    
    const decryptedData = decryptWithPublicKey(response.sign, publicKey);
    let validationResult;
    
    try {
      validationResult = JSON.parse(decryptedData);
    } catch (error) {
      validationResult = {};
    }
    
    if (validationResult.timestamp) {
      if (validationResult.errCode === "abc-error") {
        licenseCheckFailCount++;
      } else {
        licenseCheckFailCount = 0;
      }
    } else {
      licenseCheckFailCount++;
    }
    
    // 连续失败3次则关闭所有窗口
    if (licenseCheckFailCount >= 3) {
      console.warn("License validation failed multiple times, closing windows");
      windowMetaList.forEach(windowMeta => {
        if (!windowMeta.win.isDestroyed()) {
          windowMeta.win.close();
        }
      });
    }
  } catch (error) {
    console.error("License validation error:", error);
    licenseCheckFailCount++;
  }
}

// ===== 代理服务器管理 =====

/**
 * 构建代理URL
 */
function buildProxyUrl(proxyConfig) {
  if (!proxyConfig.protocol || !proxyConfig.ips) {
    return null;
  }

  const [host, port, username, password] = proxyConfig.ips.split('|');
  let auth = username && password ? username + ':' + password + '@' : '';
  return proxyConfig.protocol + "://" + auth + host + ':' + port;
}

/**
 * 查找可用端口
 */
async function findAvailablePort(startPort) {
  function tryPort(port, callback) {
    const server = net.createServer()
      .once("error", () => tryPort(port + 1, callback))
      .once("listening", () => server.close(() => callback(port)))
      .listen(port);
  }
  return new Promise(resolve => tryPort(startPort, resolve));
}

/**
 * 创建代理服务器
 */
async function createProxyServer(proxyConfig) {
  const upstreamUrl = buildProxyUrl(proxyConfig);
  if (!upstreamUrl) {
    return null;
  }

  // 复用现有代理服务器
  if (proxyServerMap.has(upstreamUrl)) {
    const existingProxy = proxyServerMap.get(upstreamUrl);
    existingProxy.refCount += 1;
    return existingProxy;
  }

  // 创建新代理服务器
  const availablePort = await findAvailablePort(nextProxyPort);
  nextProxyPort = availablePort + 1;

  const proxyServer = new ProxyChain.Server({
    port: availablePort,
    prepareRequestFunction: () => ({
      upstreamProxyUrl: upstreamUrl
    })
  });

  try {
    await proxyServer.listen();
    console.log(`Proxy server started on port ${availablePort}`);
  } catch (error) {
    console.error("ProxyChain listen error:", error);
    throw error;
  }

  const proxyInfo = {
    server: proxyServer,
    port: availablePort,
    upstream: upstreamUrl,
    refCount: 1
  };

  proxyServerMap.set(upstreamUrl, proxyInfo);
  return proxyInfo;
}

/**
 * 释放代理服务器
 */
async function releaseProxyServer(proxyConfig) {
  const upstreamUrl = buildProxyUrl(proxyConfig);
  if (!upstreamUrl) {
    return;
  }

  const proxyInfo = proxyServerMap.get(upstreamUrl);
  if (proxyInfo) {
    proxyInfo.refCount -= 1;
    if (proxyInfo.refCount <= 0) {
      try {
        await proxyInfo.server.close(true);
        console.log(`Proxy server closed: ${upstreamUrl}`);
      } catch (error) {
        console.warn("Error closing proxy server:", error);
      }
      proxyServerMap.delete(upstreamUrl);
    }
  }
}

// ===== 窗口暂停/恢复管理 =====

/**
 * 初始化窗口暂停状态
 */
function initWindowPauseState(windowId) {
  if (!windowPauseStates.has(windowId)) {
    windowPauseStates.set(windowId, {
      paused: false,
      resumeResolve: null,
      waitPromise: Promise.resolve()
    });
  }
}

/**
 * 暂停窗口自动化
 */
function pauseWindowAutomation(windowId) {
  const pauseState = windowPauseStates.get(windowId);
  if (!pauseState) {
    console.warn("Window pause state not found:", windowId);
    return;
  }

  pauseState.paused = true;
  if (!pauseState.resumeResolve) {
    pauseState.waitPromise = new Promise(resolve => pauseState.resumeResolve = resolve);
  }
  console.log(`Window ${windowId} paused`);
}

/**
 * 恢复窗口自动化
 */
function resumeWindowAutomation(windowId) {
  const pauseState = windowPauseStates.get(windowId);
  if (!pauseState) {
    console.warn("Window pause state not found:", windowId);
    return;
  }

  pauseState.paused = false;
  if (pauseState.resumeResolve) {
    pauseState.resumeResolve();
    pauseState.resumeResolve = null;
    pauseState.waitPromise = Promise.resolve();
  }
  console.log(`Window ${windowId} resumed`);
}

/**
 * 等待窗口恢复
 */
async function waitForWindowResume(windowId) {
  const pauseState = windowPauseStates.get(windowId);
  if (!pauseState) {
    console.warn("Window pause state not found:", windowId);
    return;
  }

  if (pauseState.paused) {
    await pauseState.waitPromise;
  }
}

/**
 * 清理窗口暂停状态
 */
function cleanupWindowPauseState(windowId) {
  windowPauseStates.delete(windowId);
}

// ===== 时间管理 =====

/**
 * 检查时间是否在范围内
 */
function isTimeInRange(startTime, endTime, currentTime = new Date()) {
  const [startHour, startMinute] = startTime.split(':').map(Number);
  let [endHour, endMinute] = endTime.split(':').map(Number);

  if (endHour === 24) endHour = 0;

  const currentMinutes = currentTime.getHours() * 60 + currentTime.getMinutes();
  const startMinutes = startHour * 60 + startMinute;
  const endMinutes = endHour * 60 + endMinute;

  if (startMinutes < endMinutes) {
    return currentMinutes >= startMinutes && currentMinutes < endMinutes;
  } else {
    return currentMinutes >= startMinutes || currentMinutes < endMinutes;
  }
}

/**
 * 检查账号是否在工作时间
 */
function isAccountInWorkingTime(accountId) {
  const accountList = store.get("accountList") || [];
  const account = accountList.find(acc => acc.id === accountId);

  if (!account) return false;

  const startTime = account.startTime || "08:00";
  const endTime = account.endTime || "24:00";
  return isTimeInRange(startTime, endTime);
}

// ===== 主窗口管理 =====

/**
 * 创建主窗口
 */
async function createMainWindow() {
  mainWindow = new BrowserWindow({
    width: 1200,
    height: 800,
    icon: iconPath,
    webPreferences: {
      devTools: false,
      preload: preloadPath
    },
    title: `超凡 ${CURRENT_VERSION}`
  });

  // 禁止开发者工具
  mainWindow.webContents.on("devtools-opened", () => {
    mainWindow.close();
  });

  // 加载页面
  if (VITE_DEV_SERVER_URL) {
    mainWindow.loadURL(VITE_DEV_SERVER_URL);
  } else {
    mainWindow.loadFile(indexHtmlPath);
  }

  // 设置空菜单
  const emptyMenu = Menu.buildFromTemplate([]);
  Menu.setApplicationMenu(emptyMenu);

  // 页面加载完成处理
  mainWindow.webContents.on("did-finish-load", () => {
    if (mainWindow != null) {
      mainWindow.webContents.send("main-process-message", new Date().toLocaleString());
    }
  });

  // 处理新窗口打开
  mainWindow.webContents.setWindowOpenHandler(({ url }) => {
    if (url.startsWith("https:") || url.startsWith("http:")) {
      shell.openExternal(url);
      return { action: "deny" };
    }
    console.log("Blocked non-http(s) protocol:", url);
    return { action: "deny" };
  });
}

// ===== IPC 通信处理 =====

// 存储相关
ipcMain.handle("get-store", (_, key) => store.get(key));
ipcMain.handle("set-store", (_, key, value) => {
  store.set(key, value);
  return true;
});

// 设备ID获取
ipcMain.handle("get-device-id", async () => {
  try {
    return machineIdSync(true);
  } catch (error) {
    console.error("Failed to get device ID:", error);
    return '';
  }
});

// ===== Electron 应用生命周期 =====

// 系统配置
process.env.APP_ROOT = APP_ROOT;
process.env.VITE_PUBLIC = VITE_PUBLIC;

// Windows 6.1 兼容性
if (os.release().startsWith("6.1")) {
  app.disableHardwareAcceleration();
}

// Windows 平台设置
if (process.platform === "win32") {
  app.setAppUserModelId(app.getName());
}

// 单实例锁
if (!app.requestSingleInstanceLock()) {
  app.quit();
  process.exit(0);
}

// 应用准备就绪
app.whenReady().then(async () => {
  await createMainWindow();
  await checkForUpdate();

  // 协议处理
  protocol.handle("bitbrowser", async request => {
    console.log("[main] Blocked protocol:", request.url);
    return new Response("blocked");
  });
});

// 所有窗口关闭
app.on("window-all-closed", () => {
  mainWindow = null;
  if (process.platform !== "darwin") {
    app.quit();
  }
});

// 第二个实例启动
app.on("second-instance", () => {
  if (mainWindow) {
    if (mainWindow.isMinimized()) {
      mainWindow.restore();
    }
    mainWindow.focus();
  }
});

// macOS 激活
app.on("activate", () => {
  const allWindows = BrowserWindow.getAllWindows();
  if (allWindows.length) {
    allWindows[0].focus();
  } else {
    createMainWindow();
  }
});

// ===== 定时任务 =====

// 定期验证许可证
setInterval(validateLicense, 10 * 60 * 1000); // 每10分钟检查一次

// 定期检查账号工作时间
setInterval(() => {
  const accountList = store.get("accountList") || [];
  for (const account of accountList) {
    const startTime = account.startTime || "08:00";
    const endTime = account.endTime || "24:00";
    const isInWorkingTime = isTimeInRange(startTime, endTime);

    const windowMeta = windowMetaList.find(meta => meta.id === account.id);
    if (!windowMeta) continue;

    if (isInWorkingTime && windowMeta.row.isPaused) {
      resumeWindowAutomation(account.id);
      windowMeta.row.isPaused = false;
    }

    if (!isInWorkingTime && !windowMeta.row.isPaused) {
      pauseWindowAutomation(account.id);
      windowMeta.row.isPaused = true;
    }
  }
}, 60000); // 每分钟检查一次

// ===== 导出模块 =====
export {
  MAIN_DIST,
  RENDERER_DIST,
  VITE_DEV_SERVER_URL,
  decryptWithPublicKey,
  postRequest
};
