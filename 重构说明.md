# Electron 抖音自动化工具 - 代码重构说明

## 概述

本项目是一个基于 Electron 的抖音自动化工具，经过完全重构后，代码结构更加清晰，易于维护和重新打包。

## 文件结构

```
dist-electron/main/
├── index_反.js          # 原始反混淆文件
├── index_clean.js       # 部分重构版本
└── index_final.js       # 完全重构版本（推荐使用）
```

## 重构内容

### 1. 变量和函数名还原

**原混淆名称** → **重构后名称**
- `a0_0x1f4770` → `proxyServerMap`
- `a0_0x4b4fa8` → `postRequest`
- `a0_0x1c52d2` → `decryptWithPublicKey`
- `a0_0x5055a6` → `createProxyServer`
- `a0_0x355c10` → `releaseProxyServer`
- `a0_0x1f6f76` → `pauseWindowAutomation`
- `a0_0x4d632d` → `resumeWindowAutomation`
- `a0_0x24ef87` → `waitForWindowResume`

### 2. 代码结构优化

#### 模块化组织
- **依赖导入**: 统一管理所有依赖包
- **全局变量**: 清晰的变量命名和分类
- **工具函数**: 独立的工具函数模块
- **业务逻辑**: 按功能分组的业务模块

#### 主要功能模块

1. **代理服务器管理**
   - `buildProxyUrl()`: 构建代理URL
   - `findAvailablePort()`: 查找可用端口
   - `createProxyServer()`: 创建代理服务器
   - `releaseProxyServer()`: 释放代理服务器

2. **窗口管理**
   - `initWindowPauseState()`: 初始化窗口状态
   - `pauseWindowAutomation()`: 暂停自动化
   - `resumeWindowAutomation()`: 恢复自动化
   - `waitForWindowResume()`: 等待窗口恢复

3. **许可证验证**
   - `validateLicense()`: 验证许可证
   - RSA 公钥解密功能

4. **时间管理**
   - `isTimeInRange()`: 检查时间范围
   - `isAccountInWorkingTime()`: 检查工作时间

5. **主窗口管理**
   - `createMainWindow()`: 创建主窗口
   - IPC 通信处理

### 3. 代码改进

#### 错误处理
- 添加了完善的 try-catch 错误处理
- 改进了日志输出，便于调试

#### 性能优化
- 优化了代理服务器的复用机制
- 改进了窗口状态管理

#### 可读性提升
- 添加了详细的注释
- 使用了有意义的变量名
- 统一了代码风格

## 核心功能说明

### 1. 抖音自动化
- 自动点赞、评论、收藏
- 粉丝互动自动化
- 多账号管理

### 2. 代理管理
- 支持多种代理协议
- 自动代理服务器创建和管理
- 代理IP地理位置识别

### 3. 窗口控制
- 多窗口自动排列
- 窗口暂停/恢复功能
- 工作时间控制

### 4. 数据管理
- 账号数据存储
- 缓存文件管理
- 统计数据记录

## 使用方法

### 1. 替换原文件
将 `index_final.js` 替换原来的 `index.js` 文件：

```bash
cp dist-electron/main/index_final.js dist-electron/main/index.js
```

### 2. 重新打包
使用 Electron 打包工具重新打包应用：

```bash
npm run build
npm run electron:build
```

### 3. 配置说明
主要配置项：
- `CURRENT_VERSION`: 当前版本号
- `UPDATE_SERVER`: 更新服务器地址
- `LICENSE_SERVER`: 许可证验证服务器
- `commentTemplates`: 评论模板库

## 安全注意事项

1. **许可证验证**: 保留了原有的许可证验证机制
2. **代理安全**: 代理服务器仅用于合法用途
3. **数据加密**: 敏感数据使用加密存储
4. **网络安全**: 限制了不安全的协议访问

## 依赖包说明

主要依赖：
- `electron`: Electron 框架
- `user-agents`: 用户代理生成
- `node-fetch`: HTTP 请求
- `proxy-chain`: 代理链管理
- `@nut-tree/nut-js`: 自动化操作
- `electron-store`: 数据存储
- `node-machine-id`: 设备ID获取

## 开发建议

1. **测试**: 重新打包后请充分测试所有功能
2. **备份**: 保留原始文件作为备份
3. **日志**: 注意观察控制台日志输出
4. **更新**: 定期检查依赖包更新

## 故障排除

### 常见问题

1. **代理连接失败**
   - 检查代理配置格式
   - 确认代理服务器可用性

2. **许可证验证失败**
   - 检查网络连接
   - 确认许可证密钥正确

3. **窗口创建失败**
   - 检查系统资源
   - 确认 Electron 版本兼容性

### 调试方法

1. 启用开发者工具（仅开发环境）
2. 查看控制台日志输出
3. 检查网络请求状态
4. 监控系统资源使用

## 版本信息

- **重构版本**: 2.5.0
- **重构日期**: 2025-01-03
- **兼容性**: Electron 13+, Node.js 16+

## 联系方式

如有问题或建议，请通过以下方式联系：
- 技术支持: 查看原应用中的联系方式
- 文档更新: 根据实际使用情况更新本文档

---

**注意**: 本重构版本保持了原有功能的完整性，同时提高了代码的可维护性和可读性。建议在生产环境使用前进行充分测试。
