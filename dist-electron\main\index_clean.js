/**
 * Electron 主进程 - 抖音自动化工具
 * 重构后的清晰版本，用于重新打包
 */

// ===== 依赖导入 =====
import { app, ipcMain, session, protocol, BrowserWindow, shell, screen, Menu, dialog, clipboard } from 'electron';
import UserAgent from 'user-agents';
import fetch from 'node-fetch';
import ProxyChain from 'proxy-chain';
import net from 'net';
import { createRequire } from 'node:module';
import { fileURLToPath } from 'node:url';
import path from 'node:path';
import os from 'node:os';
import fs from 'fs';
import 'fetch-blob';
import 'formdata-polyfill';
import machineId from 'node-machine-id';
import { keyboard, Key } from '@nut-tree/nut-js';
import Store from 'electron-store';
import crypto from 'node:crypto';

// ===== 全局变量 =====
const CURRENT_VERSION = "2.5.0";
const proxyServerMap = new Map(); // 代理服务器映射表
const windowPauseStates = new Map(); // 窗口暂停状态管理
const windowMetaList = []; // 窗口元数据列表
const taskQueue = []; // 任务队列
let isProcessingQueue = false; // 队列处理状态
let nextProxyPort = 18080; // 代理服务器起始端口
let mainWindow = null; // 主窗口
let licenseCheckFailCount = 0; // 许可证检查失败次数

// ===== 路径配置 =====
const __dirname = path.dirname(fileURLToPath(import.meta.url));
const APP_ROOT = path.join(__dirname, "../..");
const MAIN_DIST = path.join(APP_ROOT, "dist-electron");
const RENDERER_DIST = path.join(APP_ROOT, "dist");
const VITE_DEV_SERVER_URL = process.env.VITE_DEV_SERVER_URL;
const VITE_PUBLIC = VITE_DEV_SERVER_URL ? path.join(APP_ROOT, "public") : RENDERER_DIST;
const preloadPath = path.join(__dirname, "../preload/index.js");
const indexHtmlPath = path.join(RENDERER_DIST, "index.html");
const iconPath = app.isPackaged ? path.join(RENDERER_DIST, "favicon.ico") : path.join(__dirname, "../../favicon.ico");

// ===== 数据存储 =====
const store = new Store();
const { machineIdSync } = machineId;

// ===== 工具函数 =====

/**
 * HTTP POST 请求封装
 */
async function postRequest(url, data, headers = {}) {
  const response = await fetch(url, {
    method: "POST",
    headers: {
      'Content-Type': "application/json",
      ...headers
    },
    body: JSON.stringify(data)
  });
  
  if (!response.ok) {
    throw new Error(`HTTP error! status: ${response.status}`);
  }
  
  return await response.json();
}

/**
 * RSA 公钥解密
 */
function decryptWithPublicKey(encryptedData, publicKey) {
  console.log('密文', Buffer.from(encryptedData, "base64"));
  const encrypted = Buffer.from(encryptedData, "base64");
  const decrypted = crypto.publicDecrypt({
    key: publicKey,
    padding: crypto.constants.RSA_PKCS1_PADDING
  }, encrypted);
  return decrypted.toString("utf8");
}

/**
 * 数据编码（用于缓存文件）
 */
function encodeData(data) {
  return Buffer.from((data ^ 972106363).toString()).toString("base64");
}

/**
 * 数据解码（用于缓存文件）
 */
function decodeData(encodedData) {
  try {
    return parseInt(Buffer.from(encodedData, "base64").toString()) ^ 972106363;
  } catch {
    return 0;
  }
}

/**
 * 生成随机数
 */
function randomInt(min, max) {
  return Math.floor(min + Math.random() * (max - min + 1));
}

/**
 * 检查更新功能
 */
async function checkForUpdate() {
  try {
    const updateInfo = await postRequest("http://160.202.228.242/pc/version.php", {});
    console.log(updateInfo);
    
    const { version: latestVersion, downloadUrl } = updateInfo;
    console.info("接口返回最新版本：" + latestVersion);
    
    if (latestVersion !== CURRENT_VERSION) {
      const dialogOptions = {
        type: "info",
        title: "发现新版本",
        message: `当前版本：${CURRENT_VERSION}\n最新版本：${latestVersion}\n是否前往下载新版本？`,
        buttons: ["立即下载", "稍后更新"]
      };
      
      const { response: userChoice } = await dialog.showMessageBox(dialogOptions);
      if (userChoice === 0) {
        shell.openExternal(downloadUrl);
      }
    } else {
      console.info("当前已是最新版本，无需更新");
    }
  } catch (error) {
    console.error("检查更新失败：" + error.message);
  }
}

// ===== 代理服务器管理 =====

/**
 * 构建代理URL
 */
function buildProxyUrl(proxyConfig) {
  if (!proxyConfig.protocol || !proxyConfig.ips) {
    return null;
  }
  
  const [host, port, username, password] = proxyConfig.ips.split('|');
  let auth = username && password ? username + ':' + password + '@' : '';
  return proxyConfig.protocol + "://" + auth + host + ':' + port;
}

/**
 * 查找可用端口
 */
async function findAvailablePort(startPort) {
  function tryPort(port, callback) {
    const server = net.createServer()
      .once("error", () => tryPort(port + 1, callback))
      .once("listening", () => server.close(() => callback(port)))
      .listen(port);
  }
  return new Promise(resolve => tryPort(startPort, resolve));
}

/**
 * 创建代理服务器
 */
async function createProxyServer(proxyConfig) {
  const upstreamUrl = buildProxyUrl(proxyConfig);
  if (!upstreamUrl) {
    return null;
  }
  
  // 如果已存在相同的代理服务器，增加引用计数
  if (proxyServerMap.has(upstreamUrl)) {
    const existingProxy = proxyServerMap.get(upstreamUrl);
    existingProxy.refCount += 1;
    return existingProxy;
  }
  
  // 创建新的代理服务器
  const availablePort = await findAvailablePort(nextProxyPort);
  nextProxyPort = availablePort + 1;
  
  const proxyServer = new ProxyChain.Server({
    port: availablePort,
    prepareRequestFunction: () => ({
      upstreamProxyUrl: upstreamUrl
    })
  });
  
  try {
    await proxyServer.listen();
  } catch (error) {
    console.error("ProxyChain listen error:", error);
    throw error;
  }
  
  const proxyInfo = {
    server: proxyServer,
    port: availablePort,
    upstream: upstreamUrl,
    refCount: 1
  };
  
  proxyServerMap.set(upstreamUrl, proxyInfo);
  return proxyInfo;
}

/**
 * 释放代理服务器
 */
async function releaseProxyServer(proxyConfig) {
  const upstreamUrl = buildProxyUrl(proxyConfig);
  if (!upstreamUrl) {
    return;
  }
  
  const proxyInfo = proxyServerMap.get(upstreamUrl);
  if (proxyInfo) {
    proxyInfo.refCount -= 1;
    if (proxyInfo.refCount <= 0) {
      try {
        await proxyInfo.server.close(true);
      } catch (error) {
        console.warn("Error closing proxy server:", error);
      }
      proxyServerMap.delete(upstreamUrl);
    }
  }
}

// ===== 窗口暂停/恢复状态管理 =====

/**
 * 初始化窗口暂停状态
 */
function initWindowPauseState(windowId) {
  if (!windowPauseStates.has(windowId)) {
    windowPauseStates.set(windowId, {
      paused: false,
      resumeResolve: null,
      waitPromise: Promise.resolve()
    });
  }
}

/**
 * 暂停窗口自动化
 */
function pauseWindowAutomation(windowId) {
  const pauseState = windowPauseStates.get(windowId);
  if (!pauseState) {
    console.warn("Window pause state not found: " + windowId);
    return;
  }
  
  pauseState.paused = true;
  if (!pauseState.resumeResolve) {
    pauseState.waitPromise = new Promise(resolve => pauseState.resumeResolve = resolve);
  }
}

/**
 * 恢复窗口自动化
 */
function resumeWindowAutomation(windowId) {
  const pauseState = windowPauseStates.get(windowId);
  if (!pauseState) {
    console.warn("Window pause state not found: " + windowId);
    return;
  }
  
  pauseState.paused = false;
  if (pauseState.resumeResolve) {
    pauseState.resumeResolve();
    pauseState.resumeResolve = null;
    pauseState.waitPromise = Promise.resolve();
  }
}

/**
 * 等待窗口恢复（如果被暂停）
 */
async function waitForWindowResume(windowId) {
  const pauseState = windowPauseStates.get(windowId);
  if (!pauseState) {
    console.warn("Window pause state not found: " + windowId);
    return;
  }
  
  if (pauseState.paused) {
    await pauseState.waitPromise;
  }
}

/**
 * 清理窗口暂停状态
 */
function cleanupWindowPauseState(windowId) {
  windowPauseStates.delete(windowId);
}

// ===== 许可证验证 =====

/**
 * 许可证验证
 */
async function validateLicense() {
  const licenseKey = store.get("kami_v1");
  try {
    const response = await postRequest("https://api.dushuren.tech/douyinUserFollower/licenseKey/checkV2", {
      deviceId: machineIdSync(true),
      licenseKey: licenseKey,
      platform: 'pc'
    });

    const publicKey = `-----BEGIN PUBLIC KEY-----
MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEA9ePb4Olr+QmDKhDFTBJf
j/mQeEq9QGReGKi63Sz8h+dbk4ZAwLyAYfpdl/vAFcbstdUaxR2vFzukNRkfrKG8
xuRNrZewxVp0Wl75VUL4XsoIsY2zYvadC2Iax/kFbDzGtWZBcLSq18b9OE+CS41Z
JN2gxcQHcbZTRGbl5zvGtQZXcSamKmreiRkd3EpWWlHdDUGQH/TSgCFXkazVAR96
WYFQHRxPaWFFpJBeGscKrya/AsC37zHpC4+3rSae6TKAYRs77GOqhJJG/orREWES
EMrcg1ss5M28SGz/h1MI20kLLaQUeKtDMGpUvqlUKUige4XD4iRgvtNlljvbtXZ/
DwIDAQAB
-----END PUBLIC KEY-----`;

    const decryptedData = decryptWithPublicKey(response.sign, publicKey);
    let validationResult;

    try {
      validationResult = JSON.parse(decryptedData);
    } catch (error) {
      validationResult = {};
    }

    if (validationResult.timestamp) {
      if (validationResult.errCode === "abc-error") {
        licenseCheckFailCount++;
      } else {
        licenseCheckFailCount = 0;
      }
    } else {
      licenseCheckFailCount++;
    }

    // 如果连续失败3次，关闭所有窗口
    if (licenseCheckFailCount >= 3) {
      windowMetaList.forEach(windowMeta => {
        if (!windowMeta.win.isDestroyed()) {
          windowMeta.win.close();
        }
      });
    }
  } catch (error) {
    console.error("License validation error:", error);
  }
}

// ===== 缓存文件管理 =====

// 缓存目录配置
const cachePaths = [
  app.getPath("userData"),
  app.getPath("appData"),
  app.getPath("temp"),
  app.getPath("documents"),
  app.getPath("downloads"),
  path.join(app.getPath("home"), "AppData", "Local")
];

// 缓存文件配置
const cacheConfigs = [
  { days: 2, file: ".sys_cache_a_" },
  { days: 4, file: ".cache_b_" },
  { days: 6, file: ".log_c_" },
  { days: 8, file: ".data_d_" },
  { days: 10, file: ".runtime_e_" },
  { days: 12, file: ".tmp_f_" }
];

/**
 * 生成缓存文件路径
 */
function generateCacheFilePath(index) {
  const config = cacheConfigs[index];
  const filename = config.file + Buffer.from('jk' + index).toString("hex") + '_' + index + ".dat";
  return path.join(cachePaths[index % cachePaths.length], filename);
}

/**
 * 检查缓存文件是否过期
 */
function isCacheExpired(index) {
  const filePath = generateCacheFilePath(index);
  let timestamp = null;

  try {
    if (fs.existsSync(filePath)) {
      timestamp = decodeData(fs.readFileSync(filePath, "utf8"));
    }
  } catch (error) {
    console.warn("Cache file read error:", error);
  }

  if (!timestamp) {
    // 创建新的缓存文件
    fs.writeFileSync(filePath, encodeData(Date.now()));
    return false;
  }

  return Date.now() - timestamp >= cacheConfigs[index].days * 24 * 60 * 60 * 1000;
}

// ===== 时间管理 =====

/**
 * 检查当前时间是否在指定时间范围内
 */
function isTimeInRange(startTime, endTime, currentTime = new Date()) {
  const [startHour, startMinute] = startTime.split(':').map(Number);
  let [endHour, endMinute] = endTime.split(':').map(Number);

  if (endHour === 24) {
    endHour = 0;
  }

  const currentMinutes = currentTime.getHours() * 60 + currentTime.getMinutes();
  const startMinutes = startHour * 60 + startMinute;
  const endMinutes = endHour * 60 + endMinute;

  if (startMinutes < endMinutes) {
    return currentMinutes >= startMinutes && currentMinutes < endMinutes;
  } else {
    return currentMinutes >= startMinutes || currentMinutes < endMinutes;
  }
}

/**
 * 检查账号是否在工作时间内
 */
function isAccountInWorkingTime(accountId) {
  const accountList = store.get("accountList") || [];
  const account = accountList.find(acc => acc.id === accountId);

  if (!account) {
    return false;
  }

  const startTime = account.startTime || "08:00";
  const endTime = account.endTime || "24:00";
  return isTimeInRange(startTime, endTime);
}

// ===== 评论内容库 =====
const commentTemplates = [
  "愿家人喜乐盈满人生，春暖花开心情好，快乐长伴如星辰。",
  "愿你和家人好运常伴左右，春暖花开心情好，平安护佑每刻。",
  "祝福家人喜乐盈满人生，每天皆有好心情，福气连连好运来。",
  "你这条视频我竟然看了三遍",
  "画面挺简单但意外有感觉",
  "感觉你平时是个特别细心的人",
  "这种节奏特别舒服，挺耐看",
  "不太会拍但挺有味道的",
  "你这条我忍不住点赞了",
  "看着挺稳的人，气质也好",
  "视频有点意思，很想看下一条",
  "说不上哪里好，反正挺吸引人",
  "你笑起来挺有感染力的",
  "镜头里透着一种特别的真实",
  "你这样发很自然，让人喜欢",
  "动作不多，但挺让人想看完",
  "不刻意的样子最打动人",
  "你这风格我还真挺喜欢",
  "普通场景但拍出了感觉",
  "视频挺随意的，我却很喜欢"
];

// ===== 任务队列管理 =====

/**
 * 添加任务到队列
 */
async function addTaskToQueue(task) {
  taskQueue.push(task);
  processTaskQueue();
}

/**
 * 处理任务队列
 */
async function processTaskQueue() {
  if (isProcessingQueue || taskQueue.length === 0) {
    return;
  }

  isProcessingQueue = true;

  try {
    const task = taskQueue.shift();
    if (task) {
      await task();
    }
  } finally {
    isProcessingQueue = false;
    setTimeout(processTaskQueue, 100);
  }
}

// ===== 自动评论功能 =====

/**
 * 自动粘贴评论
 */
async function pasteComment(windowId, comment) {
  addTaskToQueue(async () => {
    const windowMeta = windowMetaList.find(meta => meta.id === windowId);
    if (!windowMeta) {
      console.log("Window meta not found for id", windowId);
      return;
    }

    if (windowMeta.win.isDestroyed()) {
      console.log("Window is destroyed for id", windowId);
      return;
    }

    const window = windowMeta.win;

    function focusWindow() {
      window.focus();
      window.show();
    }

    focusWindow();

    // 随机选择评论内容
    const randomComment = commentTemplates[Math.floor(Math.random() * commentTemplates.length)];
    clipboard.writeText(randomComment);

    await new Promise(resolve => setTimeout(resolve, Math.floor(700 + Math.random() * 301)));

    focusWindow();

    // 模拟 Ctrl+V 粘贴
    await keyboard.pressKey(Key.LeftControl, Key.V);
    await keyboard.releaseKey(Key.LeftControl, Key.V);

    await new Promise(resolve => setTimeout(resolve, Math.floor(700 + Math.random() * 301)));

    focusWindow();

    // 模拟回车发送
    await keyboard.pressKey(Key.Enter);
    await keyboard.releaseKey(Key.Enter);

    await new Promise(resolve => setTimeout(resolve, 350));
  });
}

// ===== 窗口布局管理 =====

/**
 * 自动排列窗口
 */
function arrangeWindows(startIndex = 0) {
  windowMetaList.sort((a, b) => (a.row.index ?? 0) - (b.row.index ?? 0));

  const { width: screenWidth, height: screenHeight } = screen.getPrimaryDisplay().workAreaSize;
  const windowCount = windowMetaList.length;

  if (windowCount === 0) {
    return;
  }

  const maxWindowsPerRow = Math.floor((screenWidth + 10) / 360);
  const maxWindowsTotal = maxWindowsPerRow * 2;
  const windowHeight = Math.max(450, Math.floor((screenHeight - 10) / 2));

  if (windowCount <= maxWindowsTotal) {
    // 简单布局：最多两行
    let firstRowCount = Math.min(windowCount, maxWindowsPerRow);
    let secondRowCount = windowCount > maxWindowsPerRow ? windowCount - maxWindowsPerRow : 0;
    let rowCounts = [firstRowCount, secondRowCount];

    windowMetaList.forEach((windowMeta, index) => {
      let row = 0;
      let col = index;

      if (index >= rowCounts[0]) {
        row = 1;
        col = index - rowCounts[0];
      }

      const x = col * 360;
      const y = row * (windowHeight + 10);

      const finalX = Math.min(x, screenWidth - 350);
      const finalY = Math.min(y, screenHeight - windowHeight);

      if (!windowMeta.win.isDestroyed()) {
        windowMeta.win.setBounds({
          x: finalX,
          y: finalY,
          width: 350,
          height: windowHeight
        });
        windowMeta.win.setTitle(windowMeta.id);
        windowMeta.win.show();
      }
    });
  } else {
    // 复杂布局：动态间距
    const firstRowCount = Math.ceil(windowCount / 2);
    const secondRowCount = windowCount - firstRowCount;
    const rowCounts = [firstRowCount, secondRowCount];

    const calculateSpacing = (count) => {
      if (count <= 1) return 0;
      const availableWidth = screenWidth - 350;
      return Math.max(60, Math.floor(availableWidth / (count - 1)));
    };

    windowMetaList.forEach((windowMeta, index) => {
      let row = 0;
      let col = index;

      if (index >= rowCounts[0]) {
        row = 1;
        col = index - rowCounts[0];
      }

      const rowCount = rowCounts[row];
      const spacing = calculateSpacing(rowCount);
      const x = col * spacing;
      const y = row * (windowHeight + 10);

      const finalX = Math.min(x, screenWidth - 350);
      const finalY = Math.min(y, screenHeight - windowHeight);

      if (!windowMeta.win.isDestroyed()) {
        windowMeta.win.setBounds({
          x: finalX,
          y: finalY,
          width: 350,
          height: windowHeight
        });
        windowMeta.win.setTitle(windowMeta.id);
        windowMeta.win.show();
      }
    });
  }
}

// ===== 指纹生成 =====

/**
 * 生成浏览器指纹
 */
async function generateFingerprint(accountId, proxyIp) {
  let locationData = {
    country: 'CN',
    city: "Shanghai",
    timezone: "Asia/Shanghai",
    lat: 31.2304,
    lon: 121.4737
  };

  try {
    const response = await fetch("http://ip-api.com/json/" + proxyIp + "?lang=zh-CN");
    const ipInfo = await response.json();
    if (ipInfo.status === "success") {
      locationData = {
        country: ipInfo.country ?? 'CN',
        city: ipInfo.city ?? "Shanghai",
        timezone: ipInfo.timezone ?? "Asia/Shanghai",
        lat: typeof ipInfo.lat === "number" ? ipInfo.lat : 31.2304,
        lon: typeof ipInfo.lon === "number" ? ipInfo.lon : 121.4737
      };
    }
  } catch (error) {
    console.warn("Failed to get IP location:", error);
  }

  const languageMap = {
    'CN': "zh-CN",
    'US': "en-US",
    'JP': "ja-JP",
    'RU': "ru-RU",
    'DE': "de-DE",
    'FR': "fr-FR"
  };

  const vendors = ["Intel Inc.", "NVIDIA Corporation", "ATI Technologies Inc.", "Apple Inc."];
  const renderers = ["Intel(R) Iris(TM) Plus Graphics 640", "NVIDIA GeForce GTX 1650", "AMD Radeon Pro 560", "Apple M1"];

  const vendorIndex = parseInt(accountId, 10) % vendors.length;
  const rendererIndex = parseInt(accountId, 10) % renderers.length;

  return {
    userAgent: new UserAgent({ deviceCategory: "desktop" }).toString(),
    screen: {
      width: 1366 + Math.floor(Math.random() * 300),
      height: 768 + Math.floor(Math.random() * 200)
    },
    timezone: locationData.timezone || "Asia/Shanghai",
    language: languageMap[locationData.country] || "en-US",
    geolocation: {
      lat: locationData.lat,
      lon: locationData.lon
    },
    deviceMemory: [4, 8, 16][parseInt(accountId, 10) % 3],
    hardwareConcurrency: [4, 8, 12][parseInt(accountId, 10) % 3],
    webglVendor: vendors[vendorIndex],
    webglRenderer: renderers[rendererIndex]
  };
}

// ===== 主窗口管理 =====

/**
 * 创建主窗口
 */
async function createMainWindow() {
  mainWindow = new BrowserWindow({
    width: 1200,
    height: 800,
    icon: iconPath,
    webPreferences: {
      devTools: false,
      preload: preloadPath
    },
    title: "超凡 2.0.0"
  });

  // 禁止开发者工具
  mainWindow.webContents.on("devtools-opened", () => {
    mainWindow.close();
  });

  // 加载页面
  if (VITE_DEV_SERVER_URL) {
    mainWindow.loadURL(VITE_DEV_SERVER_URL);
  } else {
    mainWindow.loadFile(indexHtmlPath);
  }

  // 设置空菜单
  const emptyMenu = Menu.buildFromTemplate([]);
  Menu.setApplicationMenu(emptyMenu);

  // 页面加载完成后的处理
  mainWindow.webContents.on("did-finish-load", () => {
    if (mainWindow != null) {
      mainWindow.webContents.send("main-process-message", new Date().toLocaleString());
    }

    // 检查缓存文件
    for (let i = 0; i < cacheConfigs.length; i++) {
      if (isCacheExpired(i)) {
        // 处理过期缓存
        console.log(`Cache ${i} expired`);
      }
    }
  });

  // 处理新窗口打开
  mainWindow.webContents.setWindowOpenHandler(({ url }) => {
    if (url.startsWith("https:") || url.startsWith("http:")) {
      shell.openExternal(url);
      return { action: "deny" };
    }
    console.log("Blocked non-http(s) protocol:", url);
    return { action: "deny" };
  });
}

/**
 * 创建账号窗口
 */
async function createAccountWindow(accountData) {
  // 检查是否已存在相同ID的窗口
  const existingWindow = windowMetaList.find(meta => meta.id === accountData.id);
  if (existingWindow && !existingWindow.win.isDestroyed()) {
    existingWindow.win.focus();
    return;
  }

  // 创建会话分区
  const partitionName = "persist:user-" + accountData.id;
  let accountSession = session.fromPartition(partitionName);

  // 设置代理
  const proxyInfo = await createProxyServer(accountData);
  if (proxyInfo) {
    await accountSession.setProxy({
      proxyRules: `http=127.0.0.1:${proxyInfo.port};https=127.0.0.1:${proxyInfo.port};`,
      proxyBypassRules: "<-loopback>"
    });
  } else {
    await accountSession.setProxy({
      proxyRules: "direct://"
    });
  }

  await new Promise(resolve => setTimeout(resolve, 1000));

  // 创建浏览器窗口
  const accountWindow = new BrowserWindow({
    icon: iconPath,
    width: 350,
    height: 450,
    x: 0,
    y: 0,
    webPreferences: {
      nativeWindowOpen: false,
      devTools: false,
      session: accountSession,
      preload: preloadPath,
      nodeIntegration: false,
      contextIsolation: true
    },
    title: `窗口${accountData.index}；${accountData.ips}；抖音：${accountData.id}`
  });

  // 禁止开发者工具
  accountWindow.webContents.on("devtools-opened", () => {
    accountWindow.close();
  });

  // 初始化窗口暂停状态
  initWindowPauseState(accountData.id);

  // 监听导航事件
  accountWindow.webContents.on("will-navigate", (event, url) => {
    if (!url.startsWith("http") && !url.startsWith("https")) {
      console.log("阻止跳转到不明协议:", url);
      event.preventDefault();
    }
  });

  // 处理新窗口打开
  accountWindow.webContents.setWindowOpenHandler(({ url }) => {
    if (!url.startsWith("http") && !url.startsWith("https")) {
      console.log("阻止打开不明协议:", url);
      return { action: "deny" };
    }
    return { action: "allow" };
  });

  // 加载空白页面
  await accountWindow.loadURL("about:blank");

  // 获取代理IP并生成指纹
  const proxyIp = await accountWindow.webContents.executeJavaScript("window.electronAPI.getProxyIp()");
  console.log(proxyIp, "proxyIp");

  const fingerprint = await generateFingerprint(accountData.id, proxyIp);
  accountWindow.webContents.send("inject-fingerprint", fingerprint);
  accountWindow.webContents.setUserAgent(fingerprint.userAgent);

  // 设置窗口标题更新
  accountWindow.on("page-title-updated", event => {
    event.preventDefault();
    const windowMeta = windowMetaList.find(meta => meta.id === accountData.id);
    const title = `窗口${windowMeta.row.index} 近期${windowMeta.row.options.fensiNum} 今日 ${windowMeta.row.options.jintianFans}`;
    accountWindow.setTitle(title);
  });

  // 加载抖音页面
  if (global.onlyBaiduProxy) {
    accountWindow.loadURL("https://www.baidu.com/s?wd=ip");
  } else {
    accountWindow.loadURL("https://www.douyin.com");
  }

  // 添加到窗口列表
  windowMetaList.push({
    id: accountData.id,
    win: accountWindow,
    row: accountData
  });

  // 窗口关闭处理
  accountWindow.on("closed", () => {
    const index = windowMetaList.findIndex(meta => meta.id === accountData.id);
    if (index !== -1) {
      windowMetaList.splice(index, 1);
    }
    arrangeWindows(0);
    cleanupWindowPauseState(accountData.id);
    releaseProxyServer(accountData);
  });

  // 页面加载完成处理
  accountWindow.webContents.on("did-finish-load", () => {
    accountWindow.webContents.send("set-win-id", accountData.id);
  });

  // 重新排列窗口
  arrangeWindows(0);
}

// ===== IPC 通信处理 =====

// 存储相关
ipcMain.handle("get-store", (event, key) => {
  return store.get(key);
});

ipcMain.handle("set-store", (event, key, value) => {
  store.set(key, value);
  return true;
});

// 设备ID获取
ipcMain.handle("get-device-id", async () => {
  try {
    return machineIdSync(true);
  } catch (error) {
    return '';
  }
});

// 评论粘贴
ipcMain.handle("pasteComment", async (event, data) => {
  await pasteComment(data.id, data.comment);
});

// 窗口控制
ipcMain.on("open-google-window", async (event, accountData) => {
  await createAccountWindow(accountData);
});

ipcMain.on("closeAll", async event => {
  windowMetaList.forEach(windowMeta => {
    if (!windowMeta.win.isDestroyed()) {
      windowMeta.win.close();
    }
  });
});

ipcMain.on("sortAll", async (event, accountList) => {
  windowMetaList.forEach(windowMeta => {
    const account = accountList.find(acc => acc.id === windowMeta.id);
    if (account && typeof account.index === "number") {
      windowMeta.row.index = account.index;
    }
  });
  arrangeWindows(0);
});

// 自动化控制
ipcMain.on("pauseAutomationDueToPopup", async (event, windowId) => {
  if (isAccountInWorkingTime(windowId)) {
    console.log("Pausing automation for window:", windowId);
    pauseWindowAutomation(windowId);
  }
});

ipcMain.on("resumeAutomationDueToPopup", async (event, windowId) => {
  if (isAccountInWorkingTime(windowId)) {
    console.log("Resuming automation for window:", windowId);
    resumeWindowAutomation(windowId);
  }
});

// ===== Electron 应用生命周期 =====

// 应用准备就绪
app.whenReady().then(createMainWindow);
app.whenReady().then(checkForUpdate);

// 协议处理
app.whenReady().then(() => {
  protocol.handle("bitbrowser", async request => {
    console.log("[main] protocol.handle intercepted:", request.url);
    return new Response("blocked");
  });
});

// 所有窗口关闭
app.on("window-all-closed", () => {
  mainWindow = null;
  if (process.platform !== "darwin") {
    app.quit();
  }
});

// 第二个实例启动
app.on("second-instance", () => {
  if (mainWindow) {
    if (mainWindow.isMinimized()) {
      mainWindow.restore();
    }
    mainWindow.focus();
  }
});

// macOS 激活
app.on("activate", () => {
  const allWindows = BrowserWindow.getAllWindows();
  if (allWindows.length) {
    allWindows[0].focus();
  } else {
    createMainWindow();
  }
});

// ===== 系统配置 =====

// 设置应用根目录
process.env.APP_ROOT = APP_ROOT;
process.env.VITE_PUBLIC = VITE_PUBLIC;

// Windows 6.1 兼容性
if (os.release().startsWith("6.1")) {
  app.disableHardwareAcceleration();
}

// Windows 平台设置
if (process.platform === "win32") {
  app.setAppUserModelId(app.getName());
}

// 单实例锁
if (!app.requestSingleInstanceLock()) {
  app.quit();
  process.exit(0);
}

// ===== 导出模块 =====
export {
  MAIN_DIST,
  RENDERER_DIST,
  VITE_DEV_SERVER_URL,
  decryptWithPublicKey,
  postRequest
};
